{"name": "regressiondbgui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "generate-routes": "tsr generate"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tailwindcss/vite": "^4.0.17", "@tanstack/react-query": "^5.71.10", "@tanstack/react-router": "^1.114.34", "@tanstack/react-table": "^8.21.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "export-to-csv": "^1.4.0", "lucide-react": "^0.486.0", "react": "^19.0.0", "react-day-picker": "^9.6.6", "react-dom": "^19.0.0", "tailwind-merge": "^3.1.0", "tailwindcss": "^4.0.17", "tw-animate-css": "^1.2.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tanstack/react-router-devtools": "^1.114.34", "@tanstack/router-plugin": "^1.114.34", "@types/node": "^22.13.16", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}