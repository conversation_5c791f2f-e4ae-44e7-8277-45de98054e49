// import <PERSON>JSO<PERSON> from "@/mock/branch.json";
// import SearchParentSuiteJSON from "@/mock/searchParentSuite.json";
// import SearchSuiteJSON from "@/mock/searchSuite.json";
// import SearchOwner from "@/mock/searchOwner.json";
// import ProfileJSON from "@/mock/myprofile.json";
// import SearchTestResults from "@/mock/searchResults.json";
import { SearchParams } from "@/routes/results";
import { TestCase } from "@/components/table/testcase-table";
// import BacklogsJSON from "@/mock/backlogs.json";
// import SearchResultsDumpPerTopo from "@/mock/searchResultsDumpPerTopo.json";
// import ActionHistoryJSON from "@/mock/actionHistory.json";
// import TestResourcesJSON from "@/mock/testresources.json";
// import RunHistoryJSON from "@/mock/runHistoryTasBranch.json";
// import TagJSON from "@/mock/tags.json";

export interface Branch {
  DT_RowId: string;
  name: string;
  email: number;
  project: string;
  state: string;
}

export interface User {
  text: "active users" | "left users";
  children?: { id: string; text: string }[];
  id?: string;
}

export interface ApiError {
  error: string;
}

export async function getBranches(): Promise<Branch[]> {
  // if (import.meta.env.MODE === "development") {
  //   return Promise.resolve(BranchJSON.data);
  // }

  const data = await (
    await fetch("/api/branch", {
      method: "GET",
      headers: {
        "Content-type": "application/json",
      },
    })
  ).json();

  return data.data;
}

export async function getParentSuiteResults(searchTerm: string): Promise<string[]> {
  if (searchTerm === "") {
    return Promise.resolve([]);
  }

  // if (import.meta.env.MODE === "development") {
  //   return Promise.resolve(SearchParentSuiteJSON);
  // }

  return await (
    await fetch(`/api/parentsuite/${searchTerm}?term=${searchTerm}&_type=query&q=${searchTerm}`, {
      method: "GET",
      credentials: "include",
      headers: {
        "Content-type": "application/json",
      },
    })
  ).json();
}

export async function getSuiteResults(searchTerm: string): Promise<string[]> {
  if (searchTerm === "") {
    return Promise.resolve([]);
  }

  // if (import.meta.env.MODE === "development") {
  //   return Promise.resolve(SearchSuiteJSON);
  // }

  return await (
    await fetch(`/api/testAndSuiteName/${searchTerm}?term=${searchTerm}&_type=query&q=${searchTerm}`, {
      method: "GET",
      credentials: "include",
      headers: {
        "Content-type": "application/json",
      },
    })
  ).json();
}

export async function getTags(searchTerm: string): Promise<string[]> {
  if (searchTerm === "") {
    return Promise.resolve([]);
  }

  // if (import.meta.env.MODE === "development") {
  //   return Promise.resolve(TagJSON as string[]);
  // }

  const params = new URLSearchParams({
    term: searchTerm,
    _type: "query",
    q: searchTerm
  });

  return await (
    await fetch(`/api/tag/${searchTerm}?${params.toString()}`, {
      method: "GET",
      credentials: "include",
      headers: {
        "Content-type": "application/json",
      },
    })
  ).json();
}

export async function getOwnerResults(searchTerm: string): Promise<User[]> {
  if (searchTerm === "") {
    return Promise.resolve([]);
  }

  // if (import.meta.env.MODE === "development") {
  //   return Promise.resolve(SearchOwner.results as User[]);
  // }

  return (
    await (
      await fetch(`/api/aowner/${searchTerm}?term=${searchTerm}&_type=query&q=${searchTerm}`, {
        method: "GET",
        credentials: "include",
        headers: {
          "Content-type": "application/json",
        },
      })
    ).json()
  ).results;
}

function makeLink(item: Record<string, unknown>, failed: boolean) {
  if (failed) {
    if (item["regressRunFail.version"]) {
      let hostname = item["testbedFail.hostname"];
      let link = item["regressRunFail.link"] as string;
      return `https://${hostname}/${link.split("/").slice(2).join("/")}/masterlog_list.html`;
    }
    return "";
  }

  if (item["regressRunPass.version"]) {
    let hostname = item["testbedPass.hostname"];
    let link = item["regressRunPass.link"] as string;
    return `https://${hostname}/${link.split("/").slice(2).join("/")}/masterlog_list.html`;
  }

  return "";
}

function formatStatus(item: Record<string, unknown>): string {
  if (!item["testAndSuite.Gstatus"]) return "";

  let status = item["testAndSuite.Gstatus"] as string;
  let severity = item["errorClass.severity"] as number;

  if (status === "FAILED") {
    status += `-${severity}`;
  }
  if (item.Grotten) {
    status = `ROTTEN-${status.startsWith("FAILED") ? status.split("-")[0].substring(0, 1) + "-" + status.split("-")[1] : status.substring(0, 1)}`;
  }

  return status;
}

function formatStatusDumpPerTopo(item: Record<string, unknown>): string {
  if (!item["tasStatus.status"]) return "";

  let status = item["tasStatus.status"] as string;
  let severity = item["runFail.severity"] as number;

  if (status === "FAILED") {
    status += `-${severity}`;
  }
  if (item["tasStatus.rotten"]) {
    status = `ROTTEN-${status.startsWith("FAILED") ? status.split("-")[0].substring(0, 1) + "-" + status.split("-")[1] : status.substring(0, 1)}`;
  }

  return status;
}

function translateResultsResponse(res: Record<string, unknown>[]): TestCase[] {
  return res.map((item) => {
    return {
      id: item.tasId || "",
      branch: item["branch.name"] || "",
      suite: item["suite.name"] || "",
      testCase: item["test.name"] || "",
      dur: item["testAndSuite.avDuration"] || "",
      lastChange: item["testAndSuite_status_pass.changedDate"] || "",
      status: formatStatus(item),
      lastP: item["regressRunPass.version"] || "",
      lastF: item["regressRunFail.version"] || "",
      lastTopo: item["topo_fail.platform"]
        ? `${item["topo_fail.platform"]}.${item["tas_status_fail.physTopo"]}.${item["tas_status_fail.subTopo"]}`
        : "",
      owner: item.owner || "",
      delegate: item["delegate"] || "",
      reason: item["testAndSuite_history.reason"] || "",
      action: item["testAndSuite_history.action"] || "",
      args: item["regressRunPass.args"] || "",
      testbed: item["regressRunPass.testbed"] || "",
      platform: "",
      physTopo: "",
      subTopo: "",
      extraKey: "",
      date: "",
      remarks: "",
      version: "",
      tasId: item.tasId,
      branch_id: item["testAndSuite.branch_id"] || 0,
      passC: item["testAndSuite.passCountTotal"] ? (item["testAndSuite.passCountTotal"] as number) : 0,
      failC: item["testAndSuite.failCountTotal"] ? (item["testAndSuite.failCountTotal"] as number) : 0,
      regLevel: item["testAndSuite.regLevel"] || "",
      errorString: item["runFail.errorString"] || "",
      lastFailedLink: makeLink(item, true),
      lastPassedLink: makeLink(item, false),
      DT_RowId: item["DT_RowId"],
    } as TestCase;
  });
}

function translateResultsResponseDumpPerTopo(res: Record<string, unknown>[]): TestCase[] {
  return res.map((item) => {
    return {
      id: item["tas.id"] || "",
      branch: item["branch.name"] || "",
      suite: item.suiteName || "",
      testCase: item["tasName"] || "",
      dur: item["tasStatus.duration"] || "",
      lastChange: item["tasStatus.changedDate"] || "",
      status: formatStatusDumpPerTopo(item),
      lastP: item["regressRunPass.version"] || "",
      lastF: item["regressRunFail.version"] || "",
      lastTopo: item["topo_fail.platform"]
        ? `${item["topo_fail.platform"]}.${item["tas_status_fail.physTopo"]}.${item["tas_status_fail.subTopo"]}`
        : "",
      owner: item.owner || "",
      delegate: item["delegate"] || "",
      reason: item["tasStatus.reason"] || "",
      action: item["tasStatus.action"] || "",
      args: item["tasStatus.status"] === "PASSED" ? item["regressRunPass.args"] : item["regressRunFail.args"],
      testbed: item["regressRunPass.testbed"] || "",
      platform: item["topo.platform"] || "",
      physTopo: item["physTopo.name"] || "",
      subTopo: item["subTopo.name"] || "",
      extraKey: item["extraKey.name"] || "",
      remarks: item["tasStatus.remarks"] || "",
      tasId: item.tasId,
      branch_id: item["testAndSuite.branch_id"] || 0,
      version: item["tasStatus.status"] === "PASSED" ? item["regressRunPass.version"] : item["regressRunFail.version"],
      date: item["tasStatus.status"] === "PASSED" ? item["regressRunPass.date"] : item["regressRunFail.date"],
      passC: item["tasStatus.passCount"] ? (item["tasStatus.passCount"] as number) : 0,
      failC: item["tasStatus.failCount"] ? (item["tasStatus.failCount"] as number) : 0,
      regLevel: item["tas.regLevel"] || "",
      errorString: item["runFail.errorString"] || "",
      lastFailedLink: makeLink(item, true),
      lastPassedLink: makeLink(item, false),
      DT_RowId: item["DT_RowId"],
    } as TestCase;
  });
}

export async function getSearchResults(search: SearchParams): Promise<TestCase[] | ApiError> {
  // if (import.meta.env.MODE === "development") {
  //   if (search.dumpPerTopo === 1) {
  //     return Promise.resolve(
  //       translateResultsResponseDumpPerTopo(SearchResultsDumpPerTopo as Record<string, unknown>[])
  //     );
  //   }
  //   return Promise.resolve(translateResultsResponse(SearchTestResults as Record<string, unknown>[]));
  // }

  let baseURL = "/api/search";
  let queryString = new URLSearchParams(search as Record<string, string>).toString();

  let results = await (
    await fetch(`${baseURL}?${queryString}`, {
      method: "GET",
      credentials: "include",
      headers: {
        "Content-type": "application/json",
      },
    })
  ).json();

  if (results && results.error) {
    return results;
  }

  return search.dumpPerTopo === 1 ? translateResultsResponseDumpPerTopo(results) : translateResultsResponse(results);
}

interface TestAndSuiteHistory {
  remarks?: string;
  lastGstatus?: string;
  reason?: string;
  action?: string;
  date?: string;
}

interface Owner {
  owner?: string;
}

interface ActionHistoryResult {
  testAndSuite_history?: TestAndSuiteHistory;
  owner?: Owner;
}

function translateActionHistory(items: ActionHistoryResult[]): TestCase[] {
  return items.map((item) => {
    return {
      remarks: item.testAndSuite_history?.remarks || "",
      status: item.testAndSuite_history?.lastGstatus || "",
      reason: item.testAndSuite_history?.reason || "",
      action: item.testAndSuite_history?.action || "",
      date: item.testAndSuite_history?.date || "",
      owner: item.owner?.owner || "",
    } as TestCase;
  });
}

export async function getActionHistory(search: SearchParams): Promise<TestCase[] | ApiError> {
  // if (import.meta.env.MODE === "development") {
  //   return Promise.resolve(translateActionHistory(ActionHistoryJSON.data as ActionHistoryResult[]));
  // }

  let results = await (
    await fetch(`/api/actionHistoryPerTas/${search.tasId}`, {
      method: "GET",
      credentials: "include",
      headers: {
        "Content-type": "application/json",
      },
    })
  ).json();

  if (results && results.error) {
    return results;
  }

  return translateActionHistory(results.data as ActionHistoryResult[]);
}

interface TestResourceItem {
  DT_RowId: string;
  run: {
    resource_id: number;
  };
  resources: {
    stcHw: number;
    stcVm: number;
    ls: number;
    IxHw: 1;
    IxVm: number;
    IxRouter: number;
    IxNet: number;
    bp: number;
    bb: number;
    changeddate: string;
  };
  resourceDuts: {
    dutlist: string;
  };
}

function translateTestResources(items: TestResourceItem[]): TestCase[] {
  return items.map((item) => {
    return {
      stcHw: item.resources?.stcHw || 0,
      stcVm: item.resources?.stcVm || 0,
      ls: item.resources?.ls || 0,
      IxHw: item.resources?.IxHw || 0,
      IxVm: item.resources?.IxVm || 0,
      IxRouter: item.resources?.IxRouter || 0,
      IxNet: item.resources?.IxNet || 0,
      bp: item.resources?.bp || 0,
      bb: item.resources?.bb || 0,
      date: item.resources?.changeddate || "",
      dutlist: item.resourceDuts?.dutlist || "",
    } as TestCase;
  });
}

export async function getTestResources(search: SearchParams): Promise<TestCase[] | ApiError> {
  // if (import.meta.env.MODE === "development") {
  //   return Promise.resolve(translateTestResources(TestResourcesJSON.data as TestResourceItem[]));
  // }

  let results = await (
    await fetch(`/api/testResources/${search.tasId}`, {
      method: "GET",
      credentials: "include",
      headers: {
        "Content-type": "application/json",
      },
    })
  ).json();

  if (results && results.error) {
    return results;
  }

  return translateTestResources(results.data as TestResourceItem[]);
}
interface RunHistoryItem {
  DT_RowId: string;
  branch: {
    name: string;
  };
  regressRun: {
    version: string;
    args: string;
    link: string;
    date: string;
    isPbuild: number;
    isRbuild: number;
    bossKey: number;
  };
  testbed: {
    name: string;
    ipAddr: string;
    hostname: string;
  };
  run: {
    status: string;
    duration: number;
    remarks: string;
  };
  errorClass: {
    errorClass: string;
  };
  physTopo: {
    name: string;
  };
  subTopo: {
    name: string;
  };
  topo: {
    platform: string;
  };
}

function makeLinkForRunHistory(item: RunHistoryItem): string {
  if (!item.testbed?.hostname || !item.regressRun?.link) return "";

  let hostname = item.testbed?.hostname as string;
  return "https://" + hostname + "/" + item.regressRun.link.split("/").slice(2).join("/");
}

export function translateRunHistory(items: RunHistoryItem[]): TestCase[] {
  return items.map((item) => {
    return {
      testbed: item.testbed?.name || "",
      remarks: item.run?.remarks || "",
      status: item.run?.status || "",
      physTopo: item.physTopo?.name || "",
      subTopo: item.subTopo?.name || "",
      platform: item.topo?.platform || "",
      dur: item.run?.duration || "",
      branch: item.branch?.name || "",
      version: item.regressRun?.version || "",
      args: item.regressRun?.args || "",
      date: item.regressRun?.date || "",
      isPbuild: item.regressRun?.isPbuild || 0,
      isRbuild: item.regressRun?.isRbuild || 0,
      bossKey: item.regressRun?.bossKey || 0,
      errorClass: item.errorClass?.errorClass || "",
      link: makeLinkForRunHistory(item),
    } as TestCase;
  });
}

export async function getRunHistory(search: SearchParams): Promise<TestCase[] | ApiError> {
  // if (import.meta.env.MODE === "development") {
  //   return Promise.resolve(translateRunHistory(RunHistoryJSON.data as RunHistoryItem[]));
  // }

  let results = await (
    await fetch(`/api/runHistoryTasBranch/${search.tasId}/${search.branch_id}`, {
      method: "GET",
      credentials: "include",
      headers: {
        "Content-type": "application/json",
      },
    })
  ).json();

  if (results && results.error) {
    return results;
  }

  return translateRunHistory(results.data as RunHistoryItem[]);
}

export type UserProfile = {
  branchData: string[];
  owner: string;
  quickMyErrors: number;
  fullName: string;
  email: string;
  project: string[];
  team1: string;
  team2: string;
  team3: string;
  team4: string;
  location: string;
  category: string;
  hideStatusOutCases: boolean;
  hideRegressIncludeFalseAndBetaTestCases: boolean;
  emailNotifications: boolean;
  hyperlinkToTestCases: boolean;
  wantToSeeIntraDayDeltaMails: boolean;
  doNotShowGhostExpandedTopos: boolean;
  aggregateVMSimTopo: boolean;
  shortQuickMyErrors: boolean;
  showHiddenBranches: boolean;
  adminUser: boolean;
  displayTableSize: number;
  ccList: string;
  inCcListOf: string;
};

export async function getUserProfile(): Promise<UserProfile> {
  // const baseData = ProfileJSON["\u0000User\u0000dbData"];

  // if (import.meta.env.MODE === "development") {
  //   return Promise.resolve({
  //     branchData: ProfileJSON["\u0000User\u0000branchData"] as string[],
  //     owner: baseData.owner || "",
  //     quickMyErrors: baseData.quickMyErrors || 0,
  //     fullName: baseData.fullName || "",
  //     email: baseData.email || "",
  //     project: baseData.project || [],
  //     team1: baseData.team1 || "",
  //     team2: baseData.team2 || "",
  //     team3: baseData.team3 || "",
  //     team4: baseData.team4 || "",
  //     location: baseData.location || "",
  //     category: baseData.category || "",
  //     hideStatusOutCases: baseData.hideOuts === 1,
  //     hideRegressIncludeFalseAndBetaTestCases: baseData.regIncl === 1,
  //     emailNotifications: baseData.emailNotification === 1,
  //     hyperlinkToTestCases: baseData.hyperlinkToTest === 1,
  //     wantToSeeIntraDayDeltaMails: baseData.deltaEmail === 1,
  //     doNotShowGhostExpandedTopos: baseData.skipExpandGhost === 1,
  //     aggregateVMSimTopo: baseData.vmTopoAggr === 1,
  //     shortQuickMyErrors: baseData.quickMyErrors === 1,
  //     showHiddenBranches: baseData.showOldBranches === 1,
  //     adminUser: baseData.adminUser === 1,
  //     displayTableSize: baseData.displayTableSize || 0,
  //     ccList: baseData.ccList || "",
  //     inCcListOf: "",
  //   });
  // }

  let result = await (
    await fetch(`/api/myProfile`, {
      method: "GET",
      credentials: "include",
      headers: {
        "Content-type": "application/json",
      },
    })
  ).json();

  const apiData = result["\u0000User\u0000dbData"];

  return {
    branchData: result["\u0000User\u0000branchData"] as string[],
    owner: apiData.owner || "",
    quickMyErrors: apiData.quickMyErrors || 0,
    fullName: apiData.fullName || "",
    email: apiData.email || "",
    project: apiData.project || [],
    team1: apiData.team1 || "",
    team2: apiData.team2 || "",
    team3: apiData.team3 || "",
    team4: apiData.team4 || "",
    location: apiData.location || "",
    category: apiData.category || "",
    hideStatusOutCases: apiData.hideOuts === 1,
    hideRegressIncludeFalseAndBetaTestCases: apiData.regIncl === 1,
    emailNotifications: apiData.emailNotification === 1,
    hyperlinkToTestCases: apiData.hyperlinkToTest === 1,
    wantToSeeIntraDayDeltaMails: apiData.deltaEmail === 1,
    doNotShowGhostExpandedTopos: apiData.skipExpandGhost === 1,
    aggregateVMSimTopo: apiData.vmTopoAggr === 1,
    shortQuickMyErrors: apiData.quickMyErrors === 1,
    showHiddenBranches: apiData.showOldBranches === 1,
    adminUser: apiData.adminUser === 1,
    displayTableSize: apiData.displayTableSize || 0,
    ccList: apiData.ccList || "",
    inCcListOf: "",
  };
}
export async function getBacklogs(): Promise<{ backlogCount: number; backlogItems: Record<string, string> }> {
  // if (import.meta.env.MODE === "development") {
  //   return Promise.resolve({
  //     backlogCount: BacklogsJSON[0] as number,
  //     backlogItems: BacklogsJSON[1] as Record<string, string>,
  //   });
  // }

  let result = await (
    await fetch(`/api/parsingBacklog`, {
      method: "GET",
      credentials: "include",
      headers: {
        "Content-type": "application/json",
      },
    })
  ).json();

  return {
    backlogCount: result[0] as number,
    backlogItems: result[1] as Record<string, string>,
  };
}
