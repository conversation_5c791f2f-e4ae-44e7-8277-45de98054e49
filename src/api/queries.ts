import { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  getOwnerResults,
  getParentSuiteResults,
  getSuiteResults,
  getUserProfile,
  getBacklogs,
  getTags, 
} from ".";

async function fetchJSON<T = any>(url: string): Promise<T> {
  const res = await fetch(url, {
    credentials: "include",
    headers: { "Content-Type": "application/json" },
  });
  if (!res.ok) throw new Error(`HTTP ${res.status} for ${url}`);
  return res.json();
}

function normalizeToStrings(payload: any): string[] {
  if (!payload) return [];
  if (Array.isArray(payload)) return payload.map(String);
  if (Array.isArray(payload?.data)) {
    return payload.data.map((x: any) =>
      typeof x === "string" ? x : x?.name ?? x?.value ?? String(x)
    );
  }
  return Object.values(payload).map((x: any) =>
    typeof x === "string" ? x : x?.name ?? x?.value ?? String(x)
  );
}

export function useOwnerQuery(searchTerm: string) {
  return useQuery({
    queryKey: ["owner", searchTerm],
    queryFn: () => getOwnerResults(searchTerm),
  });
}

export function useParentSuiteQuery(searchTerm: string) {
  return useQuery({
    queryKey: ["parentsuite", searchTerm],
    queryFn: () => getParentSuiteResults(searchTerm),
  });
}

export function useSuiteQuery(searchTerm: string) {
  return useQuery({
    queryKey: ["suite", searchTerm],
    queryFn: () => getSuiteResults(searchTerm),
  });
}

export function useTagsQuery(searchTerm: string) {
  return useQuery({
    queryKey: ["tags", "typeahead", searchTerm],
    queryFn: () => getTags(searchTerm),
    select: normalizeToStrings,
  });
}

export function useProfileQuery() {
  return useQuery({
    queryKey: ["profile"],
    queryFn: () => getUserProfile(),
  });
}

export function useBacklogsQuery() {
  return useQuery({
    queryKey: ["backlogs"],
    queryFn: () => getBacklogs(),
    refetchInterval: 120000,
  });
}

export function useTeamQuery(searchTerm: string) {
  return useQuery({
    queryKey: ["team", searchTerm],
    enabled: !!searchTerm,
    queryFn: () => fetchJSON(`/api/team/${encodeURIComponent(searchTerm)}`),
  });
}

export function usePhysTopoQuery(searchTerm: string) {
  return useQuery({
    queryKey: ["physTopo", searchTerm],
    enabled: !!searchTerm,
    queryFn: () => fetchJSON(`/api/physTopo/${encodeURIComponent(searchTerm)}`),

  });
}

export function useSubTopoQuery(searchTerm: string) {
  return useQuery({
    queryKey: ["subTopo", searchTerm],
    enabled: !!searchTerm,
    queryFn: () => fetchJSON(`/api/subTopo/${encodeURIComponent(searchTerm)}`),

  });
}

export function useReglevelsList() {
  return useQuery({
    queryKey: ["reglevels", "list"],
    queryFn: () => fetchJSON("/api/reglevels"),

  });
}

export function useTagsList() {
  return useQuery({
    queryKey: ["tags", "list"],
    queryFn: () => fetchJSON("/api/tags"),

  });
}


export function useErrorClassList() {
  return useQuery({
    queryKey: ["errorClass", "list"],
    queryFn: () => fetchJSON("/api/errorClass"),

  });
}

export function useExtraKeysList() {
  return useQuery({
    queryKey: ["extraKeys", "list"],
    queryFn: () => fetchJSON("/api/extraKeys"),
  });
}


type VmTopoRow = {
  vmHostPlatform?: { name?: string } | null;
  vmHostHypervisor?: { name?: string } | null;
  vmHostInterface?: { name?: string } | null;
};

export function useVmEnumerations() {
  const { data, isLoading, error } = useQuery<{ data?: VmTopoRow[] }>({
    queryKey: ["vmTopo"],
    queryFn: () => fetchJSON("/api/vmTopo"),
  });

  const platforms = useMemo(() => {
    const s = new Set<string>(["None"]);
    data?.data?.forEach((r) => {
      const n = r.vmHostPlatform?.name;
      if (n) s.add(String(n));
    });
    return Array.from(s);
  }, [data]);

  const hypervisors = useMemo(() => {
    const s = new Set<string>(["None"]);
    data?.data?.forEach((r) => {
      const n = r.vmHostHypervisor?.name;
      if (n) s.add(String(n));
    });
    return Array.from(s);
  }, [data]);

  const interfaces = useMemo(() => {
    const s = new Set<string>(["None"]);
    data?.data?.forEach((r) => {
      const n = r.vmHostInterface?.name;
      if (n) s.add(String(n));
    });
    return Array.from(s);
  }, [data]);

  return { platforms, hypervisors, interfaces, isLoading, error };
}

type StaticListsResponse = {
  platform: string[];
  subsystem: string[];
  frameworks: string[];
  reason: string[];
  toporeason: string[];
  errorclass: string[];
  errorseverity: number[];
};

export function useStaticLists() {
  return useQuery<StaticListsResponse>({
    queryKey: ["staticLists"],
    queryFn: () => fetchJSON("/api/staticLists"),
  });
}
