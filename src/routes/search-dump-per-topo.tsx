import { SearchForm } from "@/components/search-form";
import { createFileRoute } from "@tanstack/react-router";
import UserContext from "@/context/UserContext";
import { useContext } from "react";

export const Route = createFileRoute("/search-dump-per-topo")({
  component: RouteComponent,
});

function RouteComponent() {
  const userCtx = useContext(UserContext);
  const sortedBranches =
    userCtx && userCtx.branchData && Array.isArray(userCtx.branchData)
      ? userCtx.branchData.sort((a, b) => a.localeCompare(b, undefined, { numeric: true }))
      : [];

  return <SearchForm branches={sortedBranches} forceDumpPerTopo={true} />;
}
