import { getSearchResults } from "@/api";
import { STANDARD_TEST_TABLE_VIEW } from "@/components/table/table_views";
import { TestCase, TestCaseTable } from "@/components/table/testcase-table";
import { Spinner } from "@/components/ui/spinner";
import { SearchForm } from "@/components/search-form";
import { createFileRoute } from "@tanstack/react-router";
import { useContext } from "react";
import UserContext from "@/context/UserContext";
import { z } from "zod";

export const searchSchema = z.object({
  // basic
  branch: z.string().optional(),
  regLevel: z.string().optional(),
  lastStatus: z.string().optional(),
  ownerTest: z.string().optional(),
  parentSuite: z.string().optional(),
  suite: z.string().optional(),
  action: z.string().optional(),
  testCase: z.string().optional(),
  cmd: z.string().optional(),
  branch_id: z.number().optional(),
  tasId: z.number().optional(),

  // advanced left
  owner: z.string().optional(),
  reason: z.string().optional(),
  build: z.string().optional(),
  phystopology: z.string().optional(),
  platform: z.string().optional(),
  vmHostPlatform: z.string().optional(),
  subSystem: z.string().optional(),
  dumpPerTopo: z
    .number()
    .refine((val) => val === 0 || val === 1, {
      message: "dumpPerTopo must be 0 or 1",
    })
    .optional(),
  caseInsensitive: z.boolean().optional(),

  // advanced middle
  errorClassSeverity: z.string().optional(),
  tag: z.string().optional(),
  team: z.string().optional(),
  beforeBuild: z.string().optional(),
  subTopology: z.string().optional(),
  topoStatus: z.string().optional(),
  vmHostHypervisor: z.string().optional(),
  subSystemBranch: z.string().optional(),
  skipExpandGhost: z.boolean().optional(),
  vmTopoAggr: z.boolean().optional(),

  // advanced right
  infoLevel: z.string().optional(),
  framework: z.string().optional(),
  afterDate: z.string().optional(),
  fid: z.string().optional(),
  regIncl: z.string().optional(),
  location: z.string().optional(),
  dts: z.string().optional(),
  afterBuild: z.string().optional(),
  extraKey: z.string().optional(),
  topoLastModified: z.string().optional(),
  vmHostInterface: z.string().optional(),
  topoReason: z.string().optional(),
  ignoreInvalidSubtopos: z.boolean().optional(),
});

export type SearchParams = z.infer<typeof searchSchema>;

export const Route = createFileRoute("/results")({
  component: RouteComponent,
  validateSearch: (search: Record<string, unknown>) => searchSchema.parse(search),
  loaderDeps: ({ search }) => search,
  loader: async ({ deps }) => {
    return await getSearchResults(deps);
  },

  pendingComponent: () => {
    const searchParams = Route.useSearch();
    const userCtx = useContext(UserContext);

    const sortedBranches =
      userCtx && userCtx.branchData && Array.isArray(userCtx.branchData)
        ? userCtx.branchData.sort((a, b) => a.localeCompare(b, undefined, { numeric: true }))
        : [];

    return (
      <div className="flex flex-col gap-6">
        {/* Search Form - stays clickable during loading */}
        <SearchForm branches={sortedBranches} initialValues={searchParams} />

        {/* Loading area for results */}
        <div className="w-full grow flex justify-center items-center min-h-64">
          <Spinner size="large" className="text-primary" />
        </div>
      </div>
    );
  },
});

function RouteComponent() {
  const testData = Route.useLoaderData();
  const searchParams = Route.useSearch();
  const userCtx = useContext(UserContext);

  const sortedBranches =
    userCtx && userCtx.branchData && Array.isArray(userCtx.branchData)
      ? userCtx.branchData.sort((a, b) => a.localeCompare(b, undefined, { numeric: true }))
      : [];

  return (
    <div className="flex flex-col gap-6">
      {/* Search Form with preserved values */}
      <SearchForm branches={sortedBranches} initialValues={searchParams} />

      {/* Results Table */}
      <TestCaseTable view={STANDARD_TEST_TABLE_VIEW} data={testData as TestCase[]} title="DUMP PER TESTCASE" />
    </div>
  );
}
