import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@tanstack/react-router";
import { Search, BarChart2, Database, GitCompare, Briefcase, Shield, Calendar, List } from "lucide-react";
// import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import OptionsCard from "@/components/options-card";
import { useContext } from "react";
import UserContext from "@/context/UserContext";

export const Route = createFileRoute("/")({
  component: Index,
});

function Index() {
  const userCtx = useContext(UserContext);

  const content = [
    {
      icon: <Search className="h-4 w-4" />,
      title: "Searches",
      description: "Find what you're looking for",
      links: [
        {
          linkComponent: <Link to="/search-menu">main search</Link>,
        },
        {
          linkComponent: <Link to="/search-dump-per-topo">search and dump per topo</Link>,
        },
        {
          link_name: "search and dump per run",
        },
        {
          link_name: "search results for a specific build",
        },
        {
          link_name: "search results in different branches",
        },
        {
          link_name: "search store2db",
        },
        {
          link_name: "search on system test runs",
        },
        {
          linkComponent: <Link to="/search-team-members">search team members</Link>,
        },
        {
          link_name: "search by tagResult",
        },
      ],
    },
    {
      icon: <BarChart2 className="h-4 w-4" />,
      title: "Stats",
      description: "View error reports and statistics",
      links: [
        {
          linkComponent:
            userCtx?.quickMyErrors === 0 ? (
              <Link
                to="/results"
                search={{
                  lastStatus: "FAILED",
                  action: "empty",
                  ownerTest: userCtx?.owner || "",
                  cmd: "query",
                }}
              >
                {" "}
                my errors
              </Link>
            ) : (
              <Link
                to="/results"
                search={{
                  lastStatus: "FAILED",
                  action: "!empty",
                  ownerTest: userCtx?.owner || "",
                  cmd: "query",
                }}
              >
                my errors
              </Link>
            ),
        },
        {
          link_name: "my system bed errors",
        },
        {
          link_name: "my errors per branch",
        },
        {
          link_name: "my statistics",
        },
        {
          link_name: "statistics per branch",
        },
        {
          link_name: "statistics per build",
        },
        {
          link_name: "statistics per team",
        },
        {
          link_name: "test resources",
        },
        {
          link_name: "topo test resources",
        },
      ],
    },
    {
      icon: <List className="h-4 w-4" />,
      title: "Misc",
      description: "Other useful functions",
      links: [
        {
          link_name: "Add run to the parse queue",
        },
        {
          link_name: "my suite per priority durations",
        },
        {
          link_name: "all suites per priority durations",
        },
        {
          link_name: "suite per regLevel durations",
        },
        {
          link_name: "owner per regLevel durations",
        },
        {
          link_name: "all suites per regLevel durations",
        },
      ],
    },
    {
      icon: <Database className="h-4 w-4" />,
      title: "Dumps",
      description: "View and export data",
      links: [
        {
          link_name: "show runs",
        },
        {
          link_name: "show error classes",
        },
        {
          link_name: "show topology's",
        },
        {
          link_name: "show vm topology's",
        },
        {
          link_name: "show extraKey table",
        },
        {
          link_name: "show info on reglevels",
        },
        {
          link_name: "show info on branches",
        },
        {
          link_name: "show info on ignore For Global Status flags",
        },
        {
          link_name: "dump Topos for constraints",
        },
        {
          link_name: "dump runs for certain tagResult(s)",
        },
        {
          link_name: "dump testbeds that are blacklisted",
        },
      ],
    },
    {
      icon: <GitCompare className="h-4 w-4" />,
      title: "Compares",
      description: "Compare test results",
      links: [
        {
          link_name: "compare test between branches",
        },
        {
          link_name: "compare suite between branches",
        },
        {
          link_name: "compare results between builds",
        },
        {
          link_name: "compare results between different topo's",
        },
      ],
    },
    {
      icon: <Briefcase className="h-4 w-4" />,
      title: "Jobs",
      description: "Manage system jobs",
      links: [
        {
          link_name: "rollback run results",
        },
        {
          link_name: "blacklist a build",
        },
      ],
    },
    {
      icon: <Shield className="h-4 w-4" />,
      title: "Coverage",
      description: "Test coverage information",
      links: [
        {
          link_name: "Build Coverage Run",
        },
        {
          link_name: "Dump coverage Runs",
        },
        {
          link_name: "Explore test coverage",
        },
        {
          link_name: "Statistics on panos coverage",
        },
      ],
    },
    {
      icon: <Calendar className="h-4 w-4" />,
      title: "Scheduling Profiles",
      description: "Manage test scheduling",
      links: [
        {
          link_name: "Edit and create new profiles",
        },
        {
          link_name: "Scheduling profiles assignment",
        },
      ],
    },
    {
      icon: <Shield className="h-4 w-4" />,
      title: "Admin",
      description: "System administration tools",
      links: [
        {
          link_name: "teams administration",
        },
        {
          link_name: "tags administration",
        },
        {
          link_name: "Regress dashboard administration",
        },
        {
          link_name: "systemtestBed physTopo ownership administration",
        },
      ],
    },
  ];
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6  auto-rows-fr">
      {" "}
      {content.map((s) => (
        <OptionsCard {...s} />
      ))}
    </div>
  );
}
