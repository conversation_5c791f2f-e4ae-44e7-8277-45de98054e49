import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Link, useNavigate} from "@tanstack/react-router";
import UserContext from "@/context/UserContext";
import { useContext, useState } from "react";
import { Search } from "lucide-react";

export function MainNav() {
  const userCtx = useContext(UserContext);
  const navigate = useNavigate();
  const [testCaseInput, setTestCaseInput] = useState("");

  const handleSearchTest = () => {
    if (testCaseInput.trim()) {

        // Navigate to results if not on search page
        navigate({
          to: "/results",
          search: {
            testCase: testCaseInput.trim(),
            cmd: "query",
          },
        });
        setTestCaseInput("");
      }
    
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearchTest();
    }
  };

  return (
    <nav className="flex items-center space-x-4 lg:space-x-6">
      <Button variant="ghost" asChild>
        <Link to="/">Home</Link>
      </Button>
      <Button variant="ghost" asChild>
        <Link to="/search-menu">Search</Link>
      </Button>
      <Button variant="ghost" asChild>
        <Link
          to="/my-errors"
          search={{
            ownerTest: userCtx?.owner,
          }}
        >
          {" "}
          My Errors
        </Link>
      </Button>
      <div className="flex items-center space-x-3 ml-4">
        <Input
          placeholder="Testcase"
          value={testCaseInput}
          onChange={(e) => setTestCaseInput(e.target.value)}
          onKeyDown={handleKeyDown}
          className="w-48"
        />
        <Button
          variant="outline"
          size="sm"
          onClick={handleSearchTest}
          disabled={!testCaseInput.trim()}
        >
          <Search className="h-4 w-4 mr-1" />
          search
        </Button>
      </div>
    </nav>
  );
}
