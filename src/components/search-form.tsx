"use client";

import { useMemo, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { Search, X, Check, ChevronsUpDown } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { cn } from "@/lib/utils";
import {
  useOwnerQuery,
  useParentSuiteQuery,
  useSuiteQuery,
  useTeamQuery,
  usePhysTopoQuery,
  useSubTopoQuery,
  useTagsQuery,
  useStaticLists,
} from "@/api/queries";
import { useQueryClient, useQuery } from "@tanstack/react-query";
import { Link } from "@tanstack/react-router";
import { Input } from "./ui/input";
import { Checkbox } from "@/components/ui/checkbox";

// Wrapper component for Select with clear button
const SelectWithClear = ({
  value,
  onValueChange,
  onClear,
  placeholder,
  children,
  className = "",
}: {
  value: string;
  onValueChange: (value: string) => void;
  onClear: () => void;
  placeholder: string;
  children: React.ReactNode;
  className?: string;
}) => (
  <div className="relative">
    <Select value={value} onValueChange={onValueChange}>
      <SelectTrigger className={`w-full ${value ? "pr-12" : ""} ${className}`}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      {children}
    </Select>
    {value && (
      <button
        type="button"
        onClick={onClear}
        className="absolute right-5 top-1/2 -translate-y-1/2 p-1 hover:bg-gray-100 rounded-sm z-10"
        aria-label="Clear selection"
      >
        <X className="h-3 w-3 text-gray-500" />
      </button>
    )}
  </div>
);

// Enhanced Combobox with clear button and minimum character search
const ComboboxWithClear = ({
  value,
  onChange,
  onClear,
  placeholder,
  queryHook,
  type,
  className = "",
}: {
  value: string;
  onChange: (value: string) => void;
  onClear: () => void;
  placeholder: string;
  queryHook: any;
  type: string;
  className?: string;
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [open, setOpen] = useState(false);

  // Only search when we have 3+ characters
  const shouldSearch = searchQuery.length >= 3;
  const { data, isLoading } = queryHook(shouldSearch ? searchQuery : "");

  // Calculate dynamic empty message
  const emptyMessage = useMemo(() => {
    if (searchQuery.length === 0) {
      return "Enter 3 or more characters";
    } else if (searchQuery.length < 3) {
      const remaining = 3 - searchQuery.length;
      return `Enter ${remaining} or more character${remaining > 1 ? "s" : ""}`;
    } else if (shouldSearch && isLoading) {
      return "Searching...";
    } else {
      return "No results found.";
    }
  }, [searchQuery.length, shouldSearch, isLoading]);

  // Find the selected option's label
  const selectedLabel = useMemo(() => {
    if (!value) return "";

    // If we have data, try to find the proper label
    if (data) {
      if (type === "string") {
        const stringGroups = data as string[];
        const found = stringGroups.find((item) => item === value);
        if (found) return found;
      } else {
        const comboBoxGroups = data as any[];
        for (const group of comboBoxGroups) {
          if (group.children) {
            const option = group.children.find(
              (child: any) => child.id === value
            );
            if (option) return option.text;
          }
          // Handle direct items
          if (group.id === value) return group.text;
        }
      }
    }

    // Fallback: if no data or not found in data, return the value itself
    // This preserves the display when the combobox is closed and data is cleared
    return value;
  }, [value, data, type]);

  return (
    <div className="relative">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={`w-full justify-between ${value ? "pr-8" : ""} ${className}`}
          >
            {value ? selectedLabel : placeholder}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command shouldFilter={false}>
            <CommandInput
              placeholder={`Search ${placeholder.toLowerCase()}...`}
              value={searchQuery}
              onValueChange={setSearchQuery}
            />
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandList>
              {shouldSearch && data
                ? type === "string"
                  ? (data as string[]).map((item) => (
                      <CommandItem
                        key={item}
                        value={item}
                        onSelect={(currentValue) => {
                          onChange(currentValue === value ? "" : currentValue);
                          setOpen(false);
                        }}
                      >
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            value === item ? "opacity-100" : "opacity-0"
                          )}
                        />
                        {item}
                      </CommandItem>
                    ))
                  : (data as any[]).map((item) => {
                      if (item.children) {
                        return item.children.map((child: any) => (
                          <CommandItem
                            key={child.id}
                            value={child.id}
                            onSelect={(currentValue) => {
                              onChange(
                                currentValue === value ? "" : currentValue
                              );
                              setOpen(false);
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                value === child.id ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {child.text}
                          </CommandItem>
                        ));
                      } else {
                        return (
                          <CommandItem
                            key={item.id}
                            value={item.id}
                            onSelect={(currentValue) => {
                              onChange(
                                currentValue === value ? "" : currentValue
                              );
                              setOpen(false);
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                value === item.id ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {item.text}
                          </CommandItem>
                        );
                      }
                    })
                : null}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      {value && (
        <button
          type="button"
          onClick={onClear}
          className="absolute right-8 top-1/2 -translate-y-1/2 p-1 hover:bg-gray-100 rounded-sm z-10"
          aria-label="Clear selection"
        >
          <X className="h-3 w-3 text-gray-500" />
        </button>
      )}
    </div>
  );
};

// Enhanced Combobox with clear button and 1-character minimum search (for Location)
const ComboboxWithClearMinOne = ({
  value,
  onChange,
  onClear,
  placeholder,
  queryHook,
  type,
  className = "",
}: {
  value: string;
  onChange: (value: string) => void;
  onClear: () => void;
  placeholder: string;
  queryHook: any;
  type: string;
  className?: string;
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [open, setOpen] = useState(false);

  // Only search when we have 1+ characters
  const shouldSearch = searchQuery.length >= 1;
  const { data, isLoading } = queryHook(shouldSearch ? searchQuery : "");

  // Calculate dynamic empty message
  const emptyMessage = useMemo(() => {
    if (searchQuery.length === 0) {
      return "Enter 1 or more characters";
    } else if (shouldSearch && isLoading) {
      return "Searching...";
    } else {
      return "No results found.";
    }
  }, [searchQuery.length, shouldSearch, isLoading]);

  // Find the selected option's label
  const selectedLabel = useMemo(() => {
    if (!value || !data) return "";

    if (type === "string") {
      const stringGroups = data as string[];
      return stringGroups.find((item) => item === value) || "";
    } else {
      const comboBoxGroups = data as any[];
      for (const group of comboBoxGroups) {
        if (group.children) {
          const option = group.children.find(
            (child: any) => child.id === value
          );
          if (option) return option.text;
        }
        // Handle direct items
        if (group.id === value) return group.text;
      }
    }
    return value;
  }, [value, data, type]);

  return (
    <div className="relative">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={`w-full justify-between ${value ? "pr-8" : ""} ${className}`}
          >
            {value ? selectedLabel : placeholder}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command shouldFilter={false}>
            <CommandInput
              placeholder={`Search ${placeholder.toLowerCase()}...`}
              value={searchQuery}
              onValueChange={setSearchQuery}
            />
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandList>
              {shouldSearch && data
                ? type === "string"
                  ? (data as string[]).map((item) => (
                      <CommandItem
                        key={item}
                        value={item}
                        onSelect={(currentValue) => {
                          onChange(currentValue === value ? "" : currentValue);
                          setOpen(false);
                        }}
                      >
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            value === item ? "opacity-100" : "opacity-0"
                          )}
                        />
                        {item}
                      </CommandItem>
                    ))
                  : (data as any[]).map((item) => {
                      if (item.children) {
                        return item.children.map((child: any) => (
                          <CommandItem
                            key={child.id}
                            value={child.id}
                            onSelect={(currentValue) => {
                              onChange(
                                currentValue === value ? "" : currentValue
                              );
                              setOpen(false);
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                value === child.id ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {child.text}
                          </CommandItem>
                        ));
                      } else {
                        return (
                          <CommandItem
                            key={item.id}
                            value={item.id}
                            onSelect={(currentValue) => {
                              onChange(
                                currentValue === value ? "" : currentValue
                              );
                              setOpen(false);
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                value === item.id ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {item.text}
                          </CommandItem>
                        );
                      }
                    })
                : null}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      {value && (
        <button
          type="button"
          onClick={onClear}
          className="absolute right-8 top-1/2 -translate-y-1/2 p-1 hover:bg-gray-100 rounded-sm z-10"
          aria-label="Clear selection"
        >
          <X className="h-3 w-3 text-gray-500" />
        </button>
      )}
    </div>
  );
};

type SearchFormProps = {
  branches: string[];
  initialValues?: {
    // basic
    branch?: string;
    regLevel?: string;
    lastStatus?: string;
    ownerTest?: string;
    parentSuite?: string;
    suite?: string;
    action?: string;
    testCase?: string;

    // advanced left
    owner?: string;
    reason?: string;
    build?: string;
    phystopology?: string;
    platform?: string;
    vmPlatform?: string;
    subSystem?: string;
    dumpPerTopo?: number;
    caseInsensitive?: boolean;

    // advanced middle
    errorClassSeverity?: string;
    tag?: string;
    team?: string;
    beforeBuild?: string;
    subTopology?: string;
    topoStatus?: string;
    vmHypervisor?: string;
    subSystemBranch?: string;
    skipExpandGhost?: boolean;
    vmTopoAggr?: boolean;

    // advanced right
    infoLevel?: string;
    framework?: string;
    lastChangedSince?: string;
    fid?: string;
    pressInclude?: string;
    location?: string;
    dts?: string;
    afterBuild?: string;
    extraKey?: string;
    lastXDays?: string;
    vmInterface?: string;
    ignoreInvalidSubtopos?: boolean;
  };
};

export type Option = {
  name: string;
  value: string;
};

export const statusOptions: Option[] = [
  { name: "PASSED", value: "PASSED" },
  { name: "!PASSED", value: "!PASSED" },
  { name: "FAILED", value: "FAILED" },
  { name: "!FAILED", value: "!FAILED" },
  { name: "ROTTEN", value: "ROTTEN" },
  { name: "!ROTTEN", value: "!ROTTEN" },
  { name: "GHOST", value: "GHOST" },
  { name: "!GHOST", value: "!GHOST" },
  { name: "OUT", value: "OUT" },
  { name: "!OUT", value: "!OUT" },
  { name: "SKIPPED", value: "SKIPPED" },
  { name: "!SKIPPED", value: "!SKIPPED" },
];

export const actionOptions: Option[] = [
  { name: "empty", value: "empty" },
  { name: "!empty", value: "!empty" },
];

const infoLevelOptions: Option[] = [
  { name: "testOnly", value: "testOnly" },
  { name: "suiteOnly", value: "suiteOnly" },
  { name: "all", value: "all" },
];

const regressIncludeOptions: Option[] = [
  { name: "true", value: "true" },
  { name: "!true", value: "!true" },
  { name: "false", value: "false" },
  { name: "beta", value: "beta" },
  { name: "unknown", value: "unknown" },
];

const LOCATION_CODES = [
  "an",
  "at",
  "av",
  "ba",
  "br",
  "ch",
  "es",
  "ka",
  "mu",
  "mv",
  "na",
  "no",
  "ot",
  "pu",
  "su",
  "uk",
  "wf",
];
function useLocationQuery(term: string) {
  return useQuery({
    queryKey: ["location", term],
    queryFn: async () => {
      const t = (term ?? "").toLowerCase();
      return LOCATION_CODES.filter((c) => !t || c.includes(t));
    },
  });
}

function usePlatformQuery(term: string) {
  const { data: staticListsData } = useStaticLists();
  return useQuery({
    queryKey: ["platform", term],
    queryFn: async () => {
      const t = (term ?? "").toLowerCase();
      const platforms = staticListsData?.platform ?? [];
      return platforms
        .filter((p) => !t || p.toLowerCase().includes(t))
        .map((p) => ({ id: p, text: p === "" ? "Select..." : p }));
    },
    enabled: !!staticListsData,
  });
}

// This will be defined later in the component where vmTopoData is available

export const reglevelOptions: Option[] = [
  {
    name: "quickOnly",
    value: "quickOnly",
  },
  {
    name: "mediumOnly",
    value: "mediumOnly",
  },
  {
    name: "regularOnly",
    value: "regularOnly",
  },
  {
    name: "extensiveOnly",
    value: "extensiveOnly",
  },
  {
    name: "extremeOnly",
    value: "extremeOnly",
  },
  {
    name: "express",
    value: "express",
  },
  {
    name: "quick",
    value: "quick",
  },
  {
    name: "medium",
    value: "medium",
  },
  {
    name: "regular",
    value: "regular",
  },
  {
    name: "extensive",
    value: "extensive",
  },
  {
    name: "extreme",
    value: "extreme",
  },
  {
    name: "always",
    value: "always",
  },
];

export function SearchForm({ branches, initialValues }: SearchFormProps) {
  useQueryClient();

  const [_, setActiveTab] = useState("basic");

  // Basic tab
  const [parentSuiteValue, setParentSuiteValue] = useState(
    initialValues?.parentSuite || ""
  );
  const [ownerValue, setOwnerValue] = useState(initialValues?.ownerTest || "");
  const [suiteValue, setSuiteValue] = useState(initialValues?.suite || "");
  const [branch, setBranch] = useState(initialValues?.branch || "");
  const [status, setStatus] = useState(initialValues?.lastStatus || "");
  const [action, setAction] = useState(initialValues?.action || "");
  const [reglevel, setReglevel] = useState(initialValues?.regLevel || "");
  const [testcase, setTestCase] = useState(initialValues?.testCase || "");

  // Advanced tab — left column
  const [ownerAdv, setOwnerAdv] = useState(initialValues?.owner || "");
  const [reason, setReason] = useState(initialValues?.reason || "");
  const [build, setBuild] = useState(initialValues?.build || "");
  const [phystopology, setPhystopology] = useState(initialValues?.phystopology || "");
  const [platform, setPlatform] = useState(initialValues?.platform || "");
  const [vmPlatform, setVmPlatform] = useState(
    initialValues?.vmPlatform || "None"
  );
  const [subSystem, setSubSystem] = useState(initialValues?.subSystem || "");
  const [dumpPerTopo, setDumpPerTopo] = useState(
    initialValues?.dumpPerTopo !== undefined ? initialValues.dumpPerTopo === 1 : true
  );
  const [caseInsensitive, setCaseInsensitive] = useState(
    initialValues?.caseInsensitive || false
  );

  // Advanced tab — middle column
  const [errorClassSeverity, setErrorClassSeverity] = useState(
    initialValues?.errorClassSeverity || ""
  );
  const [tag, setTag] = useState(initialValues?.tag || "");
  const [team, setTeam] = useState(initialValues?.team || "");
  const [beforeBuild, setBeforeBuild] = useState(
    initialValues?.beforeBuild || ""
  );
  const [subTopology, setSubTopology] = useState(
    initialValues?.subTopology || ""
  );
  const [topoStatus, setTopoStatus] = useState(initialValues?.topoStatus || "");
  const [vmHypervisor, setVmHypervisor] = useState(
    initialValues?.vmHypervisor || "None"
  );
  const [subSystemBranch, setSubSystemBranch] = useState(
    initialValues?.subSystemBranch || ""
  );
  const [skipExpandGhost, setSkipExpandGhost] = useState(
    initialValues?.skipExpandGhost ?? true
  );
  const [vmTopoAggr, setVmTopoAggr] = useState(
    initialValues?.vmTopoAggr || false
  );

  // Advanced tab — right column
  const [infoLevel, setInfoLevel] = useState(
    initialValues?.infoLevel || "testOnly"
  );
  const [framework, setFramework] = useState(initialValues?.framework || "");
  const [lastChangedSince, setLastChangedSince] = useState(
    initialValues?.lastChangedSince || ""
  );

  // Check if fields should be disabled when suiteOnly is selected
  const isFieldsDisabled = infoLevel === "suiteOnly"; // yyyy-mm-dd
  const [fid, setFid] = useState(initialValues?.fid || "");
  const [regressInclude, setRegressInclude] = useState(
    initialValues?.pressInclude || ""
  );
  const [location, setLocation] = useState(initialValues?.location || "");
  const [dts, setDts] = useState(initialValues?.dts || "");
  const [afterBuild, setAfterBuild] = useState(initialValues?.afterBuild || "");
  const [extraKey, setExtraKey] = useState(initialValues?.extraKey || "");
  const [lastXDays, setLastXDays] = useState(initialValues?.lastXDays || "");
  const [vmInterface, setVmInterface] = useState(
    initialValues?.vmInterface || "None"
  );
  const [ignoreInvalidSubtopos, setIgnoreInvalidSubtopos] = useState(
    initialValues?.ignoreInvalidSubtopos || false
  );

  // --- API-backed option lists ---
  const fetchJSON = async (url: string) =>
    (
      await fetch(url, {
        credentials: "include",
        headers: { "Content-type": "application/json" },
      })
    ).json();

  const { data: reglevelsData } = useQuery({
    queryKey: ["reglevels"],
    queryFn: () => fetchJSON("/api/reglevels"),
  });

  const { data: vmTopoData } = useQuery({
    queryKey: ["vmTopo"],
    queryFn: () => fetchJSON("/api/vmTopo"),
  });

  // VM Platform query hook
  const useVmPlatformQuery = (term: string) => {
    return useQuery({
      queryKey: ["vmPlatform", term],
      queryFn: async () => {
        const t = (term ?? "").toLowerCase();
        const platforms = [
          "None",
          ...((vmTopoData?.data ?? [])
            .map((r: any) => r?.vmHostPlatform?.name)
            .filter(Boolean) as string[]),
        ];
        const uniquePlatforms = Array.from(new Set(platforms));
        return uniquePlatforms
          .filter((p) => !t || p.toLowerCase().includes(t))
          .map((p) => ({ id: p, text: p }));
      },
      enabled: !!vmTopoData,
    });
  };

  // VM Hypervisor query hook
  const useVmHypervisorQuery = (term: string) => {
    return useQuery({
      queryKey: ["vmHypervisor", term],
      queryFn: async () => {
        const t = (term ?? "").toLowerCase();
        const hypervisors = [
          "None",
          ...((vmTopoData?.data ?? [])
            .map((r: any) => r?.vmHostHypervisor?.name)
            .filter(Boolean) as string[]),
        ];
        const uniqueHypervisors = Array.from(new Set(hypervisors));
        return uniqueHypervisors
          .filter((h) => !t || h.toLowerCase().includes(t))
          .map((h) => ({ id: h, text: h }));
      },
      enabled: !!vmTopoData,
    });
  };

  // ExtraKey query hook
  const useExtraKeyQuery = (term: string) => {
    return useQuery({
      queryKey: ["extraKey", term],
      queryFn: async () => {
        const t = (term ?? "").toLowerCase();
        const response = await fetchJSON("/api/extraKeys");
        const extraKeys = (response?.data ?? [])
          .map((row: any) => row?.extraKey?.name)
          .filter((n: any) => n !== undefined) as string[];
        const uniqueKeys = Array.from(new Set(extraKeys));
        return uniqueKeys
          .filter((k: string) => !t || k.toLowerCase().includes(t))
          .map((k: string) => ({ id: k, text: k === "" ? "Select..." : k }));
      },
    });
  };

  // Static lists from API
  const { data: staticListsData } = useStaticLists();

  const uniq = <T,>(arr: T[]) => Array.from(new Set(arr));

  const reglevelsOpts: Option[] = useMemo(() => {
    const names: string[] = uniq(
      (reglevelsData?.data ?? [])
        .map((row: any) => row?.regLevel?.name)
        .filter(Boolean)
    );
    return names.map((name) => ({ name, value: name }));
  }, [reglevelsData]);

  const vmInterfaces: Option[] = useMemo(() => {
    const names: string[] = uniq([
      "None",
      ...((vmTopoData?.data ?? [])
        .map((r: any) => r?.vmHostInterface?.name)
        .filter(Boolean) as string[]),
    ]);
    return names.map((n) => ({ name: n, value: n }));
  }, [vmTopoData]);

  // Dynamic options from static lists API

  const subsystemOptions: Option[] = useMemo(() => {
    return (staticListsData?.subsystem ?? []).map((subsystem) => ({
      name: subsystem === "" ? "Select..." : subsystem,
      value: subsystem === "" ? "__EMPTY__" : subsystem,
    }));
  }, [staticListsData]);

  const frameworkOptions: Option[] = useMemo(() => {
    return (staticListsData?.frameworks ?? []).map((framework) => ({
      name: framework === "" ? "Select..." : framework,
      value: framework === "" ? "__EMPTY__" : framework,
    }));
  }, [staticListsData]);

  const reasonOptions: Option[] = useMemo(() => {
    return (staticListsData?.reason ?? []).map((reason) => ({
      name: reason === "" ? "Select..." : reason,
      value: reason === "" ? "__EMPTY__" : reason,
    }));
  }, [staticListsData]);

  const errorSeverityOptions: Option[] = useMemo(() => {
    return (staticListsData?.errorseverity ?? []).map((severity) => ({
      name: String(severity),
      value: String(severity),
    }));
  }, [staticListsData]);

  const clearState = () => {
    // basic
    setParentSuiteValue("");
    setOwnerValue("");
    setSuiteValue("");
    setBranch("");
    setStatus("");
    setReglevel("");
    setAction("");
    setTestCase("");

    // advanced left
    setOwnerAdv("");
    setReason("");
    setBuild("");
    setPhystopology("");
    setPlatform("");
    setVmPlatform("None");
    setSubSystem("");
    setDumpPerTopo(true);
    setCaseInsensitive(false);

    // advanced middle
    setErrorClassSeverity("");
    setTag("");
    setTeam("");
    setBeforeBuild("");
    setSubTopology("");
    setTopoStatus("");
    setVmHypervisor("None");
    setSubSystemBranch("");
    setSkipExpandGhost(true);
    setVmTopoAggr(false);

    // advanced right
    setInfoLevel("testOnly");
    setFramework("");
    setLastChangedSince("");
    setFid("");
    setRegressInclude("");
    setLocation("");
    setDts("");
    setAfterBuild("");
    setExtraKey("");
    setLastXDays("");
    setVmInterface("None");
    setIgnoreInvalidSubtopos(false);
  };

  return (
    <Card className="p-6 pb-24 relative grow">
      <Tabs defaultValue="basic" className="mb-6" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic" className="cursor-pointer">
            Basic Search
          </TabsTrigger>
          <TabsTrigger value="advanced" className="cursor-pointer">
            Advanced Search
          </TabsTrigger>
        </TabsList>

        {/* BASIC */}
        <TabsContent value="basic" className="mt-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div className="space-y-2">
              <label className="text-sm font-medium">Branch</label>
              <SelectWithClear
                value={branch}
                onValueChange={setBranch}
                onClear={() => setBranch("")}
                placeholder="Select..."
              >
                <SelectContent>
                  {branches
                    .filter((b) => b.length > 0)
                    .map((b) => (
                      <SelectItem key={b} value={b}>
                        {b}
                      </SelectItem>
                    ))}
                </SelectContent>
              </SelectWithClear>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <SelectWithClear
                value={status}
                onValueChange={setStatus}
                onClear={() => setStatus("")}
                placeholder="Select..."
              >
                <SelectContent>
                  {statusOptions.map((s) => (
                    <SelectItem key={s.value} value={s.value}>
                      {s.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </SelectWithClear>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Reglevel</label>
              <SelectWithClear
                value={reglevel}
                onValueChange={setReglevel}
                onClear={() => setReglevel("")}
                placeholder="Select..."
              >
                <SelectContent>
                  {(reglevelsOpts.length ? reglevelsOpts : []).map((r) => (
                    <SelectItem key={r.value} value={r.value}>
                      {r.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </SelectWithClear>
            </div>

            <div className="space-y-2">
              <label className={`text-sm font-medium ${isFieldsDisabled ? 'text-gray-400' : ''}`}>Action</label>
              <SelectWithClear
                value={isFieldsDisabled ? "" : action}
                onValueChange={isFieldsDisabled ? () => {} : setAction}
                onClear={() => setAction("")}
                placeholder="Select..."
                className={isFieldsDisabled ? 'opacity-50 cursor-not-allowed' : ''}
              >
                <SelectContent>
                  {actionOptions.map((a) => (
                    <SelectItem key={a.value} value={a.value} disabled={isFieldsDisabled}>
                      {a.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </SelectWithClear>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">TestCase</label>
              <Input
                placeholder="Search..."
                value={testcase}
                onChange={(e) => setTestCase(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Parent suite</label>
              <ComboboxWithClear
                placeholder="Select..."
                value={parentSuiteValue}
                onChange={setParentSuiteValue}
                onClear={() => setParentSuiteValue("")}
                queryHook={useParentSuiteQuery}
                type="string"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Owner</label>
              <ComboboxWithClear
                placeholder="Select..."
                value={ownerValue}
                onChange={setOwnerValue}
                onClear={() => setOwnerValue("")}
                queryHook={useOwnerQuery}
                type="user"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Suite</label>
              <ComboboxWithClear
                placeholder="Select..."
                value={suiteValue}
                onChange={setSuiteValue}
                onClear={() => setSuiteValue("")}
                queryHook={useSuiteQuery}
                type="string"
              />
            </div>
          </div>
        </TabsContent>

        {/* ADVANCED */}
        <TabsContent value="advanced" className="mt-4">
          <div className="grid grid-cols-12 gap-5">
            {/* Row 1 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className={`text-sm font-medium ${isFieldsDisabled ? 'text-gray-400' : ''}`}>
                Error class severity
              </label>
              <SelectWithClear
                value={isFieldsDisabled ? "" : errorClassSeverity}
                onValueChange={isFieldsDisabled ? () => {} : setErrorClassSeverity}
                onClear={() => setErrorClassSeverity("")}
                placeholder="Select..."
                className={isFieldsDisabled ? 'opacity-50 cursor-not-allowed' : ''}
              >
                <SelectContent>
                  {errorSeverityOptions.map((o) => (
                    <SelectItem key={o.value} value={String(o.value)} disabled={isFieldsDisabled}>
                      {o.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </SelectWithClear>
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">InfoLevel</label>
              <SelectWithClear
                value={infoLevel}
                onValueChange={setInfoLevel}
                onClear={() => setInfoLevel("")}
                placeholder="Select..."
              >
                <SelectContent>
                  {infoLevelOptions.map((o) => (
                    <SelectItem key={o.value} value={o.value}>
                      {o.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </SelectWithClear>
            </div>

            {/* Row 2 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Reason</label>
              <SelectWithClear
                value={reason}
                onValueChange={setReason}
                onClear={() => setReason("__EMPTY__")}
                placeholder="Select..."
              >
                <SelectContent>
                  {reasonOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </SelectWithClear>
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Tag</label>
              <ComboboxWithClear
                placeholder="Select or type..."
                value={tag}
                onChange={setTag}
                onClear={() => setTag("")}
                queryHook={useTagsQuery}
                type="string"
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Framework</label>
              <SelectWithClear
                value={framework}
                onValueChange={setFramework}
                onClear={() => setFramework("__EMPTY__")}
                placeholder="Select..."
              >
                <SelectContent>
                  {frameworkOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </SelectWithClear>
            </div>

            {/* Row 3 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className={`text-sm font-medium ${isFieldsDisabled ? 'text-gray-400' : ''}`}>Build</label>
              <Input
                placeholder="ex: BT-1688. Use [!build] for negative search"
                value={isFieldsDisabled ? "" : build}
                onChange={isFieldsDisabled ? () => {} : (e) => setBuild(e.target.value)}
                disabled={isFieldsDisabled}
                className={isFieldsDisabled ? 'opacity-50 cursor-not-allowed' : ''}
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Phystopology</label>
              <ComboboxWithClear
                placeholder="Select..."
                value={phystopology}
                onChange={setPhystopology}
                onClear={() => setPhystopology("")}
                queryHook={usePhysTopoQuery}
                type="string"
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Team</label>
              <ComboboxWithClear
                placeholder="Select..."
                value={team}
                onChange={setTeam}
                onClear={() => setTeam("")}
                queryHook={useTeamQuery}
                type="string"
              />
            </div>

            {/* Row 4 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Platform</label>
              <ComboboxWithClear
                placeholder="Select or type..."
                value={platform === "__EMPTY__" ? "" : platform}
                onChange={(value) =>
                  setPlatform(value === "" ? "__EMPTY__" : value)
                }
                onClear={() => setPlatform("__EMPTY__")}
                queryHook={usePlatformQuery}
                type="platform"
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className={`text-sm font-medium ${isFieldsDisabled ? 'text-gray-400' : ''}`}>Before build</label>
              <Input
                placeholder="ex: BT-1688. Use [!build] for negative search"
                value={isFieldsDisabled ? "" : beforeBuild}
                onChange={isFieldsDisabled ? () => {} : (e) => setBeforeBuild(e.target.value)}
                disabled={isFieldsDisabled}
                className={isFieldsDisabled ? 'opacity-50 cursor-not-allowed' : ''}
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Last changed since</label>
              <Input
                type="date"
                value={lastChangedSince}
                onChange={(e) => setLastChangedSince(e.target.value)}
              />
            </div>

            {/* Row 5 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">VM platform</label>
              <ComboboxWithClear
                placeholder="Select or type..."
                value={vmPlatform}
                onChange={setVmPlatform}
                onClear={() => setVmPlatform("")}
                queryHook={useVmPlatformQuery}
                type="vmplatform"
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Subtopology</label>
              <ComboboxWithClear
                placeholder="Select..."
                value={subTopology}
                onChange={setSubTopology}
                onClear={() => setSubTopology("")}
                queryHook={useSubTopoQuery}
                type="string"
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Regressinclude</label>
              <SelectWithClear
                value={regressInclude}
                onValueChange={setRegressInclude}
                onClear={() => setRegressInclude("")}
                placeholder="Select..."
              >
                <SelectContent>
                  {regressIncludeOptions.map((o) => (
                    <SelectItem key={o.value} value={o.value}>
                      {o.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </SelectWithClear>
            </div>

            {/* Row 6 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Sub system</label>
              <SelectWithClear
                value={subSystem}
                onValueChange={setSubSystem}
                onClear={() => setSubSystem("__EMPTY__")}
                placeholder="Select..."
              >
                <SelectContent>
                  {subsystemOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </SelectWithClear>
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Topo status</label>
              <SelectWithClear
                value={topoStatus}
                onValueChange={setTopoStatus}
                onClear={() => setTopoStatus("")}
                placeholder="Select..."
              >
                <SelectContent>
                  {statusOptions.map((s) => (
                    <SelectItem key={s.value} value={s.value}>
                      {s.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </SelectWithClear>
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Location</label>
              <ComboboxWithClearMinOne
                placeholder="Search..."
                value={location}
                onChange={setLocation}
                onClear={() => setLocation("")}
                queryHook={useLocationQuery}
                type="string"
              />
            </div>

            {/* Row 7 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className={`text-sm font-medium ${isFieldsDisabled ? 'text-gray-400' : ''}`}>DTS</label>
              <Input
                placeholder="Select..."
                value={isFieldsDisabled ? "" : dts}
                onChange={isFieldsDisabled ? () => {} : (e) => setDts(e.target.value)}
                disabled={isFieldsDisabled}
                className={isFieldsDisabled ? 'opacity-50 cursor-not-allowed' : ''}
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className={`text-sm font-medium ${isFieldsDisabled ? 'text-gray-400' : ''}`}>After build</label>
              <Input
                placeholder="ex: BT-1688. Use [!build] for negative search"
                value={isFieldsDisabled ? "" : afterBuild}
                onChange={isFieldsDisabled ? () => {} : (e) => setAfterBuild(e.target.value)}
                disabled={isFieldsDisabled}
                className={isFieldsDisabled ? 'opacity-50 cursor-not-allowed' : ''}
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">VM hypervisor</label>
              <ComboboxWithClear
                placeholder="Select or type..."
                value={vmHypervisor}
                onChange={setVmHypervisor}
                onClear={() => setVmHypervisor("")}
                queryHook={useVmHypervisorQuery}
                type="vmhypervisor"
              />
            </div>

            {/* Row 8 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Sub system branch</label>
              <SelectWithClear
                value={subSystemBranch}
                onValueChange={setSubSystemBranch}
                onClear={() => setSubSystemBranch("")}
                placeholder="Select..."
              >
                <SelectContent>
                  {branches
                    .filter((branch) => branch.length > 0)
                    .map((branch) => (
                      <SelectItem key={branch} value={branch}>
                        {branch}
                      </SelectItem>
                    ))}
                </SelectContent>
              </SelectWithClear>
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">ExtraKey</label>
              <ComboboxWithClear
                placeholder="Select or type..."
                value={extraKey === "__EMPTY__" ? "" : extraKey}
                onChange={(value) =>
                  setExtraKey(value === "" ? "__EMPTY__" : value)
                }
                onClear={() => setExtraKey("__EMPTY__")}
                queryHook={useExtraKeyQuery}
                type="extrakey"
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className={`text-sm font-medium ${isFieldsDisabled ? 'text-gray-400' : ''}`}>topo changes in the last x days</label>
              <Input
                type="number"
                placeholder="e.g. 7"
                min="1"
                max="365"
                value={isFieldsDisabled ? "" : lastXDays}
                onChange={isFieldsDisabled ? () => {} : (e) => setLastXDays(e.target.value)}
                disabled={isFieldsDisabled}
                className={isFieldsDisabled ? 'opacity-50 cursor-not-allowed' : ''}
              />
            </div>

            {/* Row 9 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">VM interface</label>
              <SelectWithClear
                value={vmInterface}
                onValueChange={setVmInterface}
                onClear={() => setVmInterface("")}
                placeholder="Select..."
              >
                <SelectContent>
                  {vmInterfaces.map((o) => (
                    <SelectItem key={o.value} value={o.value}>
                      {o.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </SelectWithClear>
            </div>

            {/* Checkbox group at bottom */}
            <div className="col-span-12 pt-4 mt-2 border-t">
              <div className="grid grid-cols-12 gap-4">
                <div className="flex items-center space-x-2 col-span-12 md:col-span-6 lg:col-span-4">
                  <Checkbox
                    id="dump-per-topo"
                    checked={dumpPerTopo}
                    onCheckedChange={(v) => setDumpPerTopo(!!v)}
                  />
                  <label htmlFor="dump-per-topo" className="text-sm">
                    Dump per topo
                  </label>
                </div>
                <div className="flex items-center space-x-2 col-span-12 md:col-span-6 lg:col-span-4">
                  <Checkbox
                    id="case-insensitive"
                    checked={caseInsensitive}
                    onCheckedChange={(v) => setCaseInsensitive(!!v)}
                  />
                  <label htmlFor="case-insensitive" className="text-sm">
                    Case insensitive search
                  </label>
                </div>
                <div className="flex items-center space-x-2 col-span-12 md:col-span-6 lg:col-span-4">
                  <Checkbox
                    id="skip-expand-ghost"
                    checked={skipExpandGhost}
                    onCheckedChange={(v) => setSkipExpandGhost(!!v)}
                  />
                  <label htmlFor="skip-expand-ghost" className="text-sm">
                    Skip expand and ghost
                  </label>
                </div>
                <div className="flex items-center space-x-2 col-span-12 md:col-span-6 lg:col-span-4">
                  <Checkbox
                    id="vm-topo-aggr"
                    checked={vmTopoAggr}
                    onCheckedChange={(v) => setVmTopoAggr(!!v)}
                  />
                  <label htmlFor="vm-topo-aggr" className="text-sm">
                    VmTopoAggr
                  </label>
                </div>
                <div className="flex items-center space-x-2 col-span-12 md:col-span-6 lg:col-span-4">
                  <Checkbox
                    id="ignore-invalid-subtopos"
                    checked={ignoreInvalidSubtopos}
                    onCheckedChange={(v) => setIgnoreInvalidSubtopos(!!v)}
                  />
                  <label htmlFor="ignore-invalid-subtopos" className="text-sm">
                    IgnoreInvalidSubtopos
                  </label>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <div className="absolute bottom-5 right-5 flex items-center justify-end space-x-2">
        <Button
          variant="outline"
          type="button"
          className="cursor-pointer"
          onClick={clearState}
        >
          <X className="mr-2 h-4 w-4" />
          Clear
        </Button>
        <Link
          to={"/results"}
          search={{
            // basic
            branch: branch,
            regLevel: reglevel,
            lastStatus: status,
            ownerTest: ownerValue,
            parentSuite: parentSuiteValue,
            suite: suiteValue,
            action: action,
            testCase: testcase,

            // advanced left
            owner: ownerAdv,
            reason: reason === "__EMPTY__" ? "" : reason,
            build,
            phystopology,
            platform: platform === "__EMPTY__" ? "" : platform,
            vmHostPlatform: vmPlatform,
            subSystem: subSystem === "__EMPTY__" ? "" : subSystem,
            dumpPerTopo: dumpPerTopo ? 1 : 0,
            caseInsensitive,

            // advanced middle
            errorClassSeverity,
            tag,
            team,
            beforeBuild,
            subTopology,
            topoStatus,
            vmHostHypervisor: vmHypervisor,
            subSystemBranch,
            skipExpandGhost,
            vmTopoAggr,

            // advanced right
            infoLevel,
            framework: framework === "__EMPTY__" ? "" : framework,
            afterDate: lastChangedSince,
            fid,
            regIncl: regressInclude,
            location,
            dts,
            afterBuild,
            extraKey: extraKey === "__EMPTY__" ? "" : extraKey,
            topoLastModified: lastXDays,
            vmHostInterface: vmInterface,
            ignoreInvalidSubtopos,

            cmd: "query",
          }}
        >
          <Button type="submit" className="bg-primary cursor-pointer">
            <Search className="mr-2 h-4 w-4" />
            Search
          </Button>
        </Link>
        <Link
          to={"/results"}
          search={{
            // basic
            branch: branch,
            regLevel: reglevel,
            lastStatus: status,
            ownerTest: ownerValue,
            parentSuite: parentSuiteValue,
            suite: suiteValue,
            action: action,
            testCase: testcase,

            // advanced left
            owner: ownerAdv,
            reason: reason === "__EMPTY__" ? "" : reason,
            build,
            phystopology,
            platform: platform === "__EMPTY__" ? "" : platform,
            vmHostPlatform: vmPlatform,
            subSystem: subSystem === "__EMPTY__" ? "" : subSystem,
            dumpPerTopo: 1, // Force dump per topo to be enabled
            caseInsensitive,

            // advanced middle
            errorClassSeverity,
            tag,
            team,
            beforeBuild,
            subTopology,
            topoStatus,
            vmHostHypervisor: vmHypervisor,
            subSystemBranch,
            skipExpandGhost,
            vmTopoAggr,

            // advanced right
            infoLevel,
            framework: framework === "__EMPTY__" ? "" : framework,
            afterDate: lastChangedSince,
            fid,
            regIncl: regressInclude,
            location,
            dts,
            afterBuild,
            extraKey: extraKey === "__EMPTY__" ? "" : extraKey,
            topoLastModified: lastXDays,
            vmHostInterface: vmInterface,
            ignoreInvalidSubtopos,

            cmd: "query",
          }}
        >
          <Button type="submit" variant="secondary" className="cursor-pointer">
            <Search className="mr-2 h-4 w-4" />
            Search and dump per topo
          </Button>
        </Link>
      </div>
    </Card>
  );
}
